import React, { useState, useEffect } from 'react';
import { Truck, Package, Warehouse, Clock, Shield, CheckCircle } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';

const Services: React.FC = () => {
  const { t, language } = useLanguage();
  const [servicesData, setServicesData] = useState<any>(null);

  useEffect(() => {
    const loadServicesData = async () => {
      try {
        const response = await fetch('/data/services.json');
        const data = await response.json();
        setServicesData(data);
      } catch (error) {
        console.error('Failed to load services data:', error);
      }
    };

    loadServicesData();
  }, []);

  if (!servicesData) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-amber-400"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  const mainServices = [
    {
      icon: Truck,
      title: t('services.equipmentRental.title'),
      description: t('services.equipmentRental.description'),
      image: '/images/equipment-fleet.png',
      features: [
        t('services.equipmentRental.forklifts'),
        t('services.equipmentRental.telehandlers'),
        t('services.equipmentRental.cranes')
      ]
    },
    {
      icon: Package,
      title: t('services.loadingServices.title'),
      description: t('services.loadingServices.description'),
      image: '/images/warehouse-operations.jpg',
      features: [
        'فريق مدرب ومؤهل',
        'معدات أمان حديثة',
        'كفاءة في الوقت',
        'تأمين شامل'
      ]
    },
    {
      icon: Warehouse,
      title: t('services.warehouseOrganization.title'),
      description: t('services.warehouseOrganization.description'),
      image: '/images/warehouse-operations.jpg',
      features: [
        'تحسين استغلال المساحة',
        'نظام تخزين ذكي',
        'سهولة الوصول للمواد',
        'تقليل وقت التشغيل'
      ]
    }
  ];

  const EquipmentCard = ({ equipment, category }: { equipment: any, category: string }) => (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
      <div className="relative h-48">
        <img
          src={equipment.image}
          alt={language === 'ar' ? equipment.type : equipment.typeEn}
          className="w-full h-full object-cover"
        />
        <div className="absolute top-4 right-4 bg-amber-400 text-slate-900 px-3 py-1 rounded-full text-sm font-bold">
          {language === 'ar' ? equipment.capacity : equipment.capacityEn}
        </div>
      </div>
      <div className="p-6">
        <h3 className="text-xl font-bold text-slate-900 mb-2">
          {language === 'ar' ? equipment.type : equipment.typeEn}
        </h3>
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-gray-600">
            <CheckCircle className="w-4 h-4 text-green-500 ml-2" />
            <span>الحمولة: {language === 'ar' ? equipment.capacity : equipment.capacityEn}</span>
          </div>
          <div className="flex items-center text-gray-600">
            <CheckCircle className="w-4 h-4 text-green-500 ml-2" />
            <span>الارتفاع: {language === 'ar' ? equipment.height : equipment.heightEn}</span>
          </div>
        </div>
        <div className="border-t pt-4">
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm text-gray-500">تأجير يومي</p>
              <p className="text-lg font-bold text-amber-600">{equipment.dailyRate}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">تأجير شهري</p>
              <p className="text-lg font-bold text-green-600">{equipment.monthlyRate}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-slate-900 mb-4">
            {t('services.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('services.subtitle')}
          </p>
        </div>

        {/* Main Services */}
        <div className="grid lg:grid-cols-3 gap-8 mb-20">
          {mainServices.map((service, index) => (
            <div
              key={index}
              className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              <div className="relative h-64">
                <img
                  src={service.image}
                  alt={service.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-slate-900 bg-opacity-50 flex items-center justify-center">
                  <service.icon className="w-16 h-16 text-amber-400" />
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-bold text-slate-900 mb-3">
                  {service.title}
                </h3>
                <p className="text-gray-600 mb-4 leading-relaxed">
                  {service.description}
                </p>
                <ul className="space-y-2">
                  {service.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-gray-700">
                      <CheckCircle className="w-4 h-4 text-green-500 ml-2 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>

        {/* Equipment Rental Details */}
        <div className="mb-16">
          <h3 className="text-3xl font-bold text-slate-900 text-center mb-12">
            {t('services.equipmentRental.title')}
          </h3>

          {/* Forklifts */}
          <div className="mb-12">
            <h4 className="text-2xl font-bold text-slate-800 mb-6 flex items-center">
              <Truck className="w-6 h-6 text-amber-400 ml-3" />
              {t('services.equipmentRental.forklifts')}
            </h4>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {servicesData.equipmentRental.forklifts.map((equipment: any) => (
                <EquipmentCard key={equipment.id} equipment={equipment} category="forklifts" />
              ))}
            </div>
          </div>

          {/* Telehandlers */}
          <div className="mb-12">
            <h4 className="text-2xl font-bold text-slate-800 mb-6 flex items-center">
              <Truck className="w-6 h-6 text-amber-400 ml-3" />
              {t('services.equipmentRental.telehandlers')}
            </h4>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {servicesData.equipmentRental.telehandlers.map((equipment: any) => (
                <EquipmentCard key={equipment.id} equipment={equipment} category="telehandlers" />
              ))}
            </div>
          </div>

          {/* Cranes */}
          <div className="mb-12">
            <h4 className="text-2xl font-bold text-slate-800 mb-6 flex items-center">
              <Truck className="w-6 h-6 text-amber-400 ml-3" />
              {t('services.equipmentRental.cranes')}
            </h4>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {servicesData.equipmentRental.cranes.map((equipment: any) => (
                <EquipmentCard key={equipment.id} equipment={equipment} category="cranes" />
              ))}
            </div>
          </div>
        </div>

        {/* Additional Services */}
        <div className="bg-slate-900 rounded-lg p-8 text-center">
          <h3 className="text-3xl font-bold text-white mb-6">
            خدمات إضافية متميزة
          </h3>
          <div className="grid md:grid-cols-2 gap-8">
            {servicesData.additionalServices.map((service: any) => (
              <div key={service.id} className="bg-white bg-opacity-10 rounded-lg p-6">
                <h4 className="text-xl font-bold text-amber-400 mb-3">
                  {language === 'ar' ? service.nameAr : service.nameEn}
                </h4>
                <p className="text-gray-300 mb-4">
                  {language === 'ar' ? service.descriptionAr : service.descriptionEn}
                </p>
                <ul className="space-y-2">
                  {(language === 'ar' ? service.features : service.featuresEn).map((feature: string, idx: number) => (
                    <li key={idx} className="flex items-center text-gray-300">
                      <Shield className="w-4 h-4 text-green-400 ml-2" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
