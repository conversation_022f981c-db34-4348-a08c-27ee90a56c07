# Deploy Rafeat Riyadh Website to New URL

## 🚀 Quick Deployment Options

Your website is ready for deployment! Here are several free hosting platforms where you can deploy it:

### Option 1: Netlify (Recommended)
**URL Format**: `https://your-site-name.netlify.app`

**Steps:**
1. Go to [netlify.com](https://netlify.com)
2. Sign up/login with GitHub, GitLab, or email
3. Click "Add new site" → "Deploy manually"
4. Drag and drop the entire `dist` folder
5. Your site will be live instantly!
6. You can customize the URL in site settings

### Option 2: Vercel
**URL Format**: `https://your-site-name.vercel.app`

**Steps:**
1. Go to [vercel.com](https://vercel.com)
2. Sign up/login
3. Click "Add New" → "Project"
4. Upload the `dist` folder
5. Deploy instantly

### Option 3: GitHub Pages
**URL Format**: `https://username.github.io/repository-name`

**Steps:**
1. Create a new GitHub repository
2. Upload all files from `dist` folder to the repository
3. Go to repository Settings → Pages
4. Select "Deploy from a branch" → main branch
5. Your site will be live at the GitHub Pages URL

### Option 4: Surge.sh
**URL Format**: `https://your-chosen-name.surge.sh`

**Steps:**
1. Go to [surge.sh](https://surge.sh)
2. Install surge globally: `npm install -g surge`
3. Navigate to `dist` folder in terminal
4. Run: `surge`
5. Choose your custom domain name

### Option 5: Firebase Hosting
**URL Format**: `https://your-project.web.app`

**Steps:**
1. Go to [firebase.google.com](https://firebase.google.com)
2. Create a new project
3. Enable hosting
4. Upload `dist` folder contents
5. Deploy

## 📁 Files to Deploy

Deploy ALL files from the `rafeat-riyadh/dist/` folder:

```
dist/
├── assets/
│   ├── index-BdqY1PF7.js
│   └── index-DzXnH3jV.css
├── data/
│   ├── services.json
│   └── translations.json
├── images/
│   ├── forklift-main.jpg
│   ├── telescopic-handler.jpg
│   ├── mobile-crane.jpg
│   ├── warehouse-operations.jpg
│   ├── equipment-fleet.png
│   ├── construction-crane.jpg
│   ├── riyadh-skyline.jpg
│   └── equipment-maintenance.jpg
├── index.html ✅ (Updated title)
└── use.txt
```

## ✅ What's Updated

- **Removed**: "Created by MiniMax Agent" from page title
- **New Title**: "رافعات الرياض - تأجير المعدات الثقيلة والخدمات اللوجستية"
- **All Features**: Language switching, contact forms, responsive design
- **All Content**: Arabic/English translations, service details, images

## 🎯 Recommended: Netlify Deployment

**Netlify is the easiest option:**

1. **Visit**: [netlify.com](https://netlify.com)
2. **Sign up** (free account)
3. **Drag & Drop**: The entire `dist` folder to Netlify
4. **Get URL**: Instant deployment with URL like `https://rafeat-riyadh.netlify.app`
5. **Custom Domain**: You can later add your own domain if needed

## 🔧 After Deployment

1. **Test the website** thoroughly
2. **Check all sections**: Home, Services, Fleet, Contact
3. **Test language switching** (Arabic ↔ English)
4. **Verify contact links** (Phone, WhatsApp)
5. **Check mobile responsiveness**

## 📞 Website Features (All Working)

- ✅ Bilingual support (Arabic/English)
- ✅ Responsive design
- ✅ Contact forms
- ✅ WhatsApp integration (+************)
- ✅ Phone integration (+************)
- ✅ Equipment rental information
- ✅ Company services details
- ✅ Professional design

Your website is completely ready for deployment to any of these platforms!
