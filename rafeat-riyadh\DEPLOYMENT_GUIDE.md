# Deployment Guide for Rafeat Riyadh Website

## Target URL
`https://we92gqirtm.space.minimax.io/`

## Files Ready for Deployment
All website files are built and ready in the `dist/` folder:

```
dist/
├── assets/
│   ├── index-BdqY1PF7.js
│   └── index-DzXnH3jV.css
├── data/
│   ├── services.json
│   └── translations.json
├── images/
│   └── [all website images]
├── index.html (✅ Updated - removed "Created by MiniMax Agent")
└── use.txt
```

## Deployment Options

### Option 1: Manual Upload via Web Interface
1. Access the MiniMax deployment dashboard
2. Navigate to your space: `we92gqirtm.space.minimax.io`
3. Upload all files from the `dist/` folder
4. Ensure the folder structure is maintained

### Option 2: Using MiniMax CLI (if available)
```bash
# Install MiniMax CLI (if not already installed)
npm install -g @minimax/cli

# Deploy to your space
minimax deploy dist/ --space=we92gqirtm

# Or alternative command structure
minimax upload dist/ --target=we92gqirtm.space.minimax.io
```

### Option 3: Using Git-based Deployment
If MiniMax supports Git deployment:
```bash
# Add deployment remote
git remote add minimax https://deploy.minimax.io/we92gqirtm.git

# Push to deploy
git add dist/
git commit -m "Update website - removed MiniMax Agent branding"
git push minimax main
```

## What's Updated
- ✅ Removed "Created by MiniMax Agent" from page title
- ✅ Updated title to: "رافعات الرياض - تأجير المعدات الثقيلة والخدمات اللوجستية"
- ✅ All website functionality preserved
- ✅ Arabic/English language support maintained
- ✅ All images and data files included

## Verification Steps
After deployment:
1. Visit `https://we92gqirtm.space.minimax.io/`
2. Check browser tab title shows the new Arabic title
3. Verify all sections load correctly
4. Test language switching (Arabic/English)
5. Test contact forms and WhatsApp/phone links

## Troubleshooting
- If deployment fails, ensure all files in `dist/` folder are included
- Check that `index.html` is in the root of the deployment
- Verify file permissions allow reading
- Ensure the space URL `we92gqirtm` is correct and accessible

## Contact Information
If you need access to the MiniMax deployment dashboard or CLI tools, you may need to:
1. Log into your MiniMax Agent account
2. Access the deployment/hosting section
3. Look for space management or file upload options
