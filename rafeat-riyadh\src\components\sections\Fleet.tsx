import React from 'react';
import { Truck, ArrowUp, Weight, Clock } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';

const Fleet: React.FC = () => {
  const { t } = useLanguage();

  const fleetCategories = [
    {
      id: 'forklift',
      title: t('fleet.forklift.title'),
      image: '/images/forklift-main.jpg',
      capacity: t('fleet.forklift.capacity'),
      height: t('fleet.forklift.height'),
      icon: Truck,
      features: [
        'تقنية هيدروليكية متطورة',
        'نظام أمان متقدم',
        'تحكم دقيق',
        'كفاءة في استهلاك الوقود'
      ],
      specifications: [
        { label: 'نوع الوقود', value: 'ديزل / كهربائي' },
        { label: 'نوع الإطارات', value: 'مطاطية / صلبة' },
        { label: 'كابينة القيادة', value: 'مغلقة / مفتوحة' },
        { label: 'نظام التوجيه', value: 'هيدروليكي' }
      ]
    },
    {
      id: 'telehandler',
      title: t('fleet.telehandler.title'),
      image: '/images/telescopic-handler.jpg',
      capacity: t('fleet.telehandler.capacity'),
      height: t('fleet.telehandler.height'),
      icon: ArrowUp,
      features: [
        'ذراع تلسكوبي قابل للتمديد',
        'قدرة على الدوران 360 درجة',
        'ملحقات متنوعة',
        'مناسب للأماكن الضيقة'
      ],
      specifications: [
        { label: 'نوع المحرك', value: 'ديزل توربو' },
        { label: 'ناقل الحركة', value: 'أوتوماتيكي' },
        { label: 'نظام الدفع', value: 'رباعي' },
        { label: 'نظام التعليق', value: 'هيدروليكي' }
      ]
    },
    {
      id: 'crane',
      title: t('fleet.crane.title'),
      image: '/images/mobile-crane.jpg',
      capacity: t('fleet.crane.capacity'),
      height: t('fleet.crane.height'),
      icon: Weight,
      features: [
        'ذراع تلسكوبي عالي الأداء',
        'نظام تحكم حاسوبي',
        'أجهزة استشعار للأمان',
        'قدرة رفع استثنائية'
      ],
      specifications: [
        { label: 'نوع الذراع', value: 'تلسكوبي هيدروليكي' },
        { label: 'عدد المحاور', value: '3-5 محاور' },
        { label: 'نظام التوازن', value: 'إلكتروني متقدم' },
        { label: 'كابينة المشغل', value: 'مكيفة مع نظام تحكم' }
      ]
    }
  ];

  const additionalFeatures = [
    {
      icon: Clock,
      title: 'صيانة دورية',
      description: 'برنامج صيانة منتظم لضمان أفضل أداء'
    },
    {
      icon: Weight,
      title: 'فحص دوري',
      description: 'فحص شامل قبل كل عملية تأجير'
    },
    {
      icon: Truck,
      title: 'تسليم سريع',
      description: 'خدمة توصيل المعدات في أقل وقت ممكن'
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-gray-100">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-slate-900 mb-4">
            {t('fleet.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('fleet.subtitle')}
          </p>
        </div>

        {/* Fleet Categories */}
        <div className="space-y-16">
          {fleetCategories.map((category, index) => (
            <div
              key={category.id}
              className={`grid lg:grid-cols-2 gap-12 items-center ${
                index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
              }`}
            >
              {/* Image */}
              <div className={`${index % 2 === 1 ? 'lg:col-start-2' : ''}`}>
                <div className="relative group">
                  <img
                    src={category.image}
                    alt={category.title}
                    className="w-full h-96 object-cover rounded-lg shadow-xl group-hover:shadow-2xl transition-shadow duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-slate-900 via-transparent to-transparent rounded-lg">
                    <div className="absolute bottom-6 left-6 right-6">
                      <div className="flex items-center justify-between text-white">
                        <div className="bg-amber-400 p-3 rounded-full">
                          <category.icon className="w-6 h-6 text-slate-900" />
                        </div>
                        <div className="text-right">
                          <p className="text-sm opacity-90">متوفر للإيجار</p>
                          <p className="text-lg font-bold">24/7</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className={`${index % 2 === 1 ? 'lg:col-start-1' : ''}`}>
                <div className="bg-white rounded-lg shadow-lg p-8">
                  <h3 className="text-3xl font-bold text-slate-900 mb-4">
                    {category.title}
                  </h3>
                  
                  {/* Key Specs */}
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="bg-amber-50 p-4 rounded-lg">
                      <p className="text-sm text-gray-600 mb-1">الحمولة القصوى</p>
                      <p className="text-lg font-bold text-amber-600">{category.capacity}</p>
                    </div>
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <p className="text-sm text-gray-600 mb-1">ارتفاع الرفع</p>
                      <p className="text-lg font-bold text-blue-600">{category.height}</p>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="mb-6">
                    <h4 className="text-lg font-bold text-slate-800 mb-3">المميزات الرئيسية</h4>
                    <ul className="grid grid-cols-2 gap-2">
                      {category.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-gray-700">
                          <div className="w-2 h-2 bg-amber-400 rounded-full ml-2"></div>
                          <span className="text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Specifications */}
                  <div>
                    <h4 className="text-lg font-bold text-slate-800 mb-3">المواصفات التقنية</h4>
                    <div className="grid grid-cols-1 gap-2">
                      {category.specifications.map((spec, idx) => (
                        <div key={idx} className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                          <span className="text-gray-600">{spec.label}</span>
                          <span className="font-medium text-slate-800">{spec.value}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Features */}
        <div className="mt-20">
          <h3 className="text-3xl font-bold text-slate-900 text-center mb-12">
            خدمات إضافية مع جميع المعدات
          </h3>
          <div className="grid md:grid-cols-3 gap-8">
            {additionalFeatures.map((feature, index) => (
              <div
                key={index}
                className="bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl transition-shadow duration-300"
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-amber-400 to-amber-500 rounded-full mb-4">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-xl font-bold text-slate-900 mb-3">
                  {feature.title}
                </h4>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Fleet Statistics */}
        <div className="mt-20 bg-slate-900 rounded-lg p-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            <div>
              <p className="text-3xl lg:text-4xl font-bold text-amber-400 mb-2">50+</p>
              <p className="text-white">معدة متوفرة</p>
            </div>
            <div>
              <p className="text-3xl lg:text-4xl font-bold text-amber-400 mb-2">24/7</p>
              <p className="text-white">خدمة مستمرة</p>
            </div>
            <div>
              <p className="text-3xl lg:text-4xl font-bold text-amber-400 mb-2">98%</p>
              <p className="text-white">معدل الرضا</p>
            </div>
            <div>
              <p className="text-3xl lg:text-4xl font-bold text-amber-400 mb-2">5+</p>
              <p className="text-white">سنوات خبرة</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Fleet;
