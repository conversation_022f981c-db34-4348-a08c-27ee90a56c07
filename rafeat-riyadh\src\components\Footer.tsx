import React from 'react';
import { Phone, MessageCircle, MapPin, Mail, Clock } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const Footer: React.FC = () => {
  const { t } = useLanguage();

  const quickLinks = [
    { label: t('nav.home'), id: 'home' },
    { label: t('nav.services'), id: 'services' },
    { label: t('nav.fleet'), id: 'fleet' },
    { label: t('nav.contact'), id: 'contact' }
  ];

  const services = [
    'تأجير الرافعات الشوكية',
    'تأجير الرافعات التلسكوبية',
    'تأجير الكرينات المتحركة',
    'خدمات التحميل والتنزيل',
    'تنظيم المستودعات'
  ];

  const handleWhatsApp = () => {
    window.open('https://wa.me/966541850468', '_blank');
  };

  const handleCall = () => {
    window.open('tel:+966508152107', '_blank');
  };

  return (
    <footer className="bg-slate-900 text-white">
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="mb-6">
              <h3 className="text-2xl font-bold mb-2">
                <span className="text-amber-400">رافعات</span>
                <span className="text-white mr-2">الرياض</span>
              </h3>
              <p className="text-gray-300 mb-4">
                {t('footer.description')}
              </p>
              <p className="text-amber-400 font-semibold">
                "حلول رفع وتأجير موثوقة لمشاريعكم"
              </p>
            </div>

            {/* Social Actions */}
            <div className="flex flex-col space-y-3">
              <button
                onClick={handleCall}
                className="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors"
              >
                <Phone className="w-4 h-4 ml-2" />
                <span>اتصال مباشر</span>
              </button>
              <button
                onClick={handleWhatsApp}
                className="flex items-center px-4 py-2 bg-green-500 hover:bg-green-600 rounded-lg transition-colors"
              >
                <MessageCircle className="w-4 h-4 ml-2" />
                <span>محادثة واتساب</span>
              </button>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-bold mb-6 text-amber-400">روابط سريعة</h4>
            <ul className="space-y-3">
              {quickLinks.map((link) => (
                <li key={link.id}>
                  <a
                    href={`#${link.id}`}
                    className="text-gray-300 hover:text-amber-400 transition-colors"
                  >
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-bold mb-6 text-amber-400">خدماتنا</h4>
            <ul className="space-y-3">
              {services.map((service, index) => (
                <li key={index}>
                  <span className="text-gray-300 text-sm">
                    {service}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-bold mb-6 text-amber-400">معلومات التواصل</h4>
            <div className="space-y-4">
              <div className="flex items-start">
                <MapPin className="w-5 h-5 text-amber-400 mt-1 ml-3 flex-shrink-0" />
                <div>
                  <p className="text-gray-300 text-sm">
                    {t('contact.address')}
                  </p>
                </div>
              </div>

              <div className="flex items-center">
                <Phone className="w-5 h-5 text-amber-400 ml-3 flex-shrink-0" />
                <div>
                  <p className="text-gray-300 text-sm">{t('contact.phone')}</p>
                </div>
              </div>

              <div className="flex items-center">
                <MessageCircle className="w-5 h-5 text-amber-400 ml-3 flex-shrink-0" />
                <div>
                  <p className="text-gray-300 text-sm">{t('contact.whatsapp')}</p>
                </div>
              </div>

              <div className="flex items-start">
                <Clock className="w-5 h-5 text-amber-400 mt-1 ml-3 flex-shrink-0" />
                <div>
                  <p className="text-gray-300 text-sm">
                    ساعات العمل: 24/7<br />
                    طوال أيام الأسبوع
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-400 text-sm mb-4 md:mb-0">
              {t('footer.rights')}
            </div>
            <div className="flex items-center space-x-6">
              <span className="text-gray-400 text-sm">
                مرخص من وزارة التجارة السعودية
              </span>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-green-400 text-sm">متاح الآن</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
