import React, { useState } from 'react';
import { Menu, X, Globe, Phone, MessageCircle } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

interface HeaderProps {
  currentSection: string;
  setCurrentSection: (section: string) => void;
}

const Header: React.FC<HeaderProps> = ({ currentSection, setCurrentSection }) => {
  const { language, toggleLanguage, t, isRTL } = useLanguage();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const navigation = [
    { id: 'home', label: t('nav.home') },
    { id: 'services', label: t('nav.services') },
    { id: 'fleet', label: t('nav.fleet') },
    { id: 'contact', label: t('nav.contact') }
  ];

  const handleSectionChange = (sectionId: string) => {
    setCurrentSection(sectionId);
    setIsMenuOpen(false);
  };

  const handleWhatsApp = () => {
    window.open('https://wa.me/966541850468', '_blank');
  };

  const handleCall = () => {
    window.open('tel:+966508152107', '_blank');
  };

  return (
    <header className="bg-slate-900 shadow-lg sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <div className="text-2xl font-bold text-white">
              <span className="text-amber-400">رافعات</span>
              <span className="text-white mr-2">الرياض</span>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => (
              <button
                key={item.id}
                onClick={() => handleSectionChange(item.id)}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  currentSection === item.id
                    ? 'bg-amber-400 text-slate-900'
                    : 'text-white hover:text-amber-400 hover:bg-slate-800'
                }`}
              >
                {item.label}
              </button>
            ))}
          </nav>

          {/* Contact Actions & Language Toggle */}
          <div className="hidden md:flex items-center space-x-4">
            <button
              onClick={handleCall}
              className="flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <Phone className="w-4 h-4 mr-2" />
              <span className="text-sm">اتصال</span>
            </button>
            
            <button
              onClick={handleWhatsApp}
              className="flex items-center px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              <span className="text-sm">واتساب</span>
            </button>

            <button
              onClick={toggleLanguage}
              className="flex items-center px-3 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              <Globe className="w-4 h-4 mr-2" />
              <span className="text-sm">{language === 'ar' ? 'EN' : 'عربي'}</span>
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-white hover:text-amber-400 p-2"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden bg-slate-800 border-t border-slate-700">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navigation.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleSectionChange(item.id)}
                  className={`block w-full text-right px-3 py-2 rounded-md text-base font-medium transition-colors ${
                    currentSection === item.id
                      ? 'bg-amber-400 text-slate-900'
                      : 'text-white hover:text-amber-400 hover:bg-slate-700'
                  }`}
                >
                  {item.label}
                </button>
              ))}
              
              <div className="flex flex-col space-y-2 pt-4 border-t border-slate-700">
                <button
                  onClick={handleCall}
                  className="flex items-center justify-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Phone className="w-4 h-4 ml-2" />
                  <span>اتصال مباشر</span>
                </button>
                
                <button
                  onClick={handleWhatsApp}
                  className="flex items-center justify-center px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                >
                  <MessageCircle className="w-4 h-4 ml-2" />
                  <span>محادثة واتساب</span>
                </button>

                <button
                  onClick={toggleLanguage}
                  className="flex items-center justify-center px-3 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
                >
                  <Globe className="w-4 h-4 ml-2" />
                  <span>{language === 'ar' ? 'English' : 'عربي'}</span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
