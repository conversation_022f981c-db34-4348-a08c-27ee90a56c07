import React from 'react';
import { ArrowLeft, ArrowRight, Clock, Users, Truck, Award } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';

interface HeroProps {
  setCurrentSection: (section: string) => void;
}

const Hero: React.FC<HeroProps> = ({ setCurrentSection }) => {
  const { t, isRTL } = useLanguage();

  const features = [
    {
      icon: Clock,
      title: t('whyChooseUs.precision.title'),
      description: t('whyChooseUs.precision.description')
    },
    {
      icon: Users,
      title: t('whyChooseUs.professional.title'),
      description: t('whyChooseUs.professional.description')
    },
    {
      icon: Truck,
      title: t('whyChooseUs.modern.title'),
      description: t('whyChooseUs.modern.description')
    },
    {
      icon: Award,
      title: t('whyChooseUs.competitive.title'),
      description: t('whyChooseUs.competitive.description')
    }
  ];

  const handleCTA = () => {
    setCurrentSection('contact');
  };

  const ArrowIcon = isRTL ? ArrowLeft : ArrowRight;

  return (
    <section className="relative min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Background Image Overlay */}
      <div className="absolute inset-0 bg-black bg-opacity-50">
        <img
          src="/images/riyadh-skyline.jpg"
          alt="Riyadh Skyline"
          className="w-full h-full object-cover opacity-30"
        />
      </div>

      {/* Content */}
      <div className="relative container mx-auto px-4 py-20">
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
          {/* Text Content */}
          <div className="text-center lg:text-right">
            <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              {t('hero.title')}
            </h1>
            
            <p className="text-xl lg:text-2xl text-amber-400 mb-6 font-semibold">
              {t('hero.subtitle')}
            </p>
            
            <p className="text-lg text-gray-300 mb-8 leading-relaxed max-w-2xl mx-auto lg:mx-0">
              {t('hero.description')}
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <button
                onClick={handleCTA}
                className="bg-amber-400 hover:bg-amber-500 text-slate-900 px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 transform hover:scale-105 flex items-center justify-center"
              >
                {t('hero.ctaButton')}
                <ArrowIcon className="w-5 h-5 mr-2" />
              </button>
              
              <button
                onClick={() => setCurrentSection('contact')}
                className="border-2 border-white text-white hover:bg-white hover:text-slate-900 px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300"
              >
                {t('hero.contactButton')}
              </button>
            </div>
          </div>

          {/* Image Content */}
          <div className="relative">
            <div className="grid grid-cols-2 gap-4">
              <img
                src="/images/equipment-fleet.png"
                alt="Equipment Fleet"
                className="rounded-lg shadow-xl transform hover:scale-105 transition-transform duration-300"
              />
              <img
                src="/images/forklift-main.jpg"
                alt="Forklift"
                className="rounded-lg shadow-xl transform hover:scale-105 transition-transform duration-300 mt-8"
              />
              <img
                src="/images/telescopic-handler.jpg"
                alt="Telescopic Handler"
                className="rounded-lg shadow-xl transform hover:scale-105 transition-transform duration-300 -mt-8"
              />
              <img
                src="/images/mobile-crane.jpg"
                alt="Mobile Crane"
                className="rounded-lg shadow-xl transform hover:scale-105 transition-transform duration-300"
              />
            </div>
          </div>
        </div>

        {/* Why Choose Us Section */}
        <div className="mt-20">
          <h2 className="text-3xl lg:text-4xl font-bold text-white text-center mb-12">
            {t('whyChooseUs.title')}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="bg-white bg-opacity-10 backdrop-blur-lg rounded-lg p-6 text-center hover:bg-opacity-20 transition-all duration-300 transform hover:scale-105"
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-amber-400 rounded-full mb-4">
                  <feature.icon className="w-8 h-8 text-slate-900" />
                </div>
                <h3 className="text-xl font-bold text-white mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-300 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
