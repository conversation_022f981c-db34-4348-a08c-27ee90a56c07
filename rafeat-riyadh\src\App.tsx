import React, { useState, useEffect } from 'react';
import { LanguageProvider } from './contexts/LanguageContext';
import Header from './components/Header';
import Hero from './components/sections/Hero';
import Services from './components/sections/Services';
import Fleet from './components/sections/Fleet';
import Contact from './components/sections/Contact';
import Footer from './components/Footer';

function App() {
  const [currentSection, setCurrentSection] = useState('home');

  useEffect(() => {
    // Smooth scroll behavior
    const handleHashChange = () => {
      const hash = window.location.hash.replace('#', '');
      if (hash) {
        setCurrentSection(hash);
      }
    };

    window.addEventListener('hashchange', handleHashChange);
    return () => window.removeEventListener('hashchange', handleHashChange);
  }, []);

  useEffect(() => {
    // Update URL hash when section changes
    if (currentSection !== 'home') {
      window.history.pushState(null, '', `#${currentSection}`);
    } else {
      window.history.pushState(null, '', window.location.pathname);
    }

    // Scroll to section
    const element = document.getElementById(currentSection);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    } else if (currentSection === 'home') {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [currentSection]);

  return (
    <LanguageProvider>
      <div className="min-h-screen bg-white">
        <Header 
          currentSection={currentSection} 
          setCurrentSection={setCurrentSection} 
        />
        
        <main>
          <section id="home">
            <Hero setCurrentSection={setCurrentSection} />
          </section>
          
          <section id="services">
            <Services />
          </section>
          
          <section id="fleet">
            <Fleet />
          </section>
          
          <section id="contact">
            <Contact />
          </section>
        </main>
        
        <Footer />
      </div>
    </LanguageProvider>
  );
}

export default App;
